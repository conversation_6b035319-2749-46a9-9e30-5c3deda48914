<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tautan ke file CSS eksternal -->
    <link rel="stylesheet" href="style.css">
</head>
<body class="text-gray-200">

    <header class="bg-gray-900/50 border-b border-gray-700/50 backdrop-blur-sm sticky top-0 z-20">
        <div class="container mx-auto max-w-5xl flex items-center justify-between p-4 relative">
             <div class="text-left">
                 <h1 class="text-xl font-bold text-white">Prompt Generator</h1>
                 <p class="text-sm text-gray-400 hidden sm:block">Buat prompt video sinematik dalam Bahasa Indonesia dan Inggris.</p>
             </div>
             <div class="flex items-center space-x-3">
                 <button id="guideBtn" title="Buka Panduan" class="bg-sky-600 hover:bg-sky-700 text-white font-bold h-8 w-8 rounded-full text-lg transition-colors flex items-center justify-center shadow-lg">?</button>
                <div class="flex items-center space-x-2 bg-gray-800 px-3 py-1.5 rounded-full border border-gray-700 shadow-lg">
                    <span class="text-yellow-400">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"/>
                          <path d="M8 13.5a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11m0 .5A6 6 0 1 0 8 2a6 6 0 0 0 0 12"/>
                          <path d="M8 11.732a.75.75 0 0 1 .75-.75h.025a.75.75 0 0 1 0 1.5H8.75a.75.75 0 0 1-.75-.75M6.625 5.34a.75.75 0 0 0-1.5 0v1.445a.75.75 0 0 0 1.5 0z"/>
                          <path d="M9.19 8.28a.75.75 0 0 0-1.06-1.06l-1.445 1.444a.75.75 0 1 0 1.06 1.06z"/>
                        </svg>
                    </span>
                    <span id="coinCount" class="font-semibold text-white">0</span>
                </div>
                <button id="addCoinBtn" title="Tambah 5 Koin" class="bg-green-500 hover:bg-green-600 text-white font-bold px-4 py-2 rounded-lg text-sm transition-colors flex items-center justify-center shadow-lg">
                    + Tambah Koin
                </button>
             </div>
             <div id="noCoinsNotification" class="absolute top-full right-4 mt-2 bg-red-600 text-white text-xs font-bold px-3 py-1.5 rounded-lg shadow-lg hidden">
                 Koin habis! Klik '+ Tambah Koin' untuk menambah.
                 <div class="absolute -top-1 right-3 w-3 h-3 bg-red-600 transform rotate-45"></div>
             </div>
        </div>
    </header>

    <main class="container mx-auto max-w-5xl p-4 sm:p-6 lg:p-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-5 bg-gray-800/50 p-6 rounded-xl border border-gray-700">

                <div>
                    <label class="block mb-2 text-sm font-medium text-gray-300">Deskripsikan dari Gambar (1 Koin)</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md">
                        <div id="imageUploadContainer" class="space-y-1 text-center">
                             <img id="imagePreview" src="" class="hidden max-h-40 mx-auto mb-4 rounded"/>
                            <svg id="imageUploadIcon" class="mx-auto h-12 w-12 text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 4v.01M28 8l-6-6-6 6M28 8v12a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32 4l-6-6-6 6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-400">
                                <label for="imageUploadInput" class="relative cursor-pointer bg-gray-700 rounded-md font-medium text-indigo-400 hover:text-indigo-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-offset-gray-900 focus-within:ring-indigo-500 px-1">
                                    <span>Unggah file</span>
                                    <input id="imageUploadInput" name="imageUploadInput" type="file" class="sr-only" accept="image/*">
                                </label>
                                <p class="pl-1">atau tarik dan lepas</p>
                            </div>
                            <p class="text-xs text-gray-500">PNG, JPG, GIF hingga 10MB</p>
                        </div>
                    </div>
                     <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3">
                         <button id="describeSubjectBtn" class="w-full text-white bg-pink-600 hover:bg-pink-700 focus:ring-4 focus:outline-none focus:ring-pink-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300" disabled>
                            Deskripsikan Subjek
                        </button>
                         <button id="describePlaceBtn" class="w-full text-white bg-cyan-600 hover:bg-cyan-700 focus:ring-4 focus:outline-none focus:ring-cyan-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300" disabled>
                            Deskripsikan Tempat
                        </button>
                     </div>
                </div>
                <hr class="border-gray-700"/>

                <h2 class="text-lg font-semibold text-white -mb-2">Isi Manual</h2>
                <div>
                    <label for="subjek" class="block mb-2 text-sm font-medium text-gray-300">Subjek (Siapa/Apa)</label>
                    <input type="text" id="subjek" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: Seekor rubah oranye yang cerdas">
                    <div class="flex space-x-2 mt-2">
                        <button id="saveCharacterBtn" class="flex-1 text-white bg-purple-600 hover:bg-purple-700 focus:ring-4 focus:outline-none focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Simpan Karakter
                        </button>
                        <button id="loadCharacterBtn" class="flex-1 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Muat Karakter
                        </button>
                    </div>
                    <div id="saveCharacterModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 hidden">
                        <div class="bg-gray-800 text-gray-300 rounded-lg shadow-2xl p-6 w-full max-w-sm max-h-[80vh] overflow-y-auto">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Simpan Karakter</h2>
                                <button id="closeSaveModalBtn" class="text-gray-400 hover:text-white text-3xl">&times;</button>
                            </div>
                            <label for="characterNameInput" class="block mb-2 text-sm font-medium text-gray-300">Nama Karakter:</label>
                            <input type="text" id="characterNameInput" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: Rubah Cerdas">
                            <button id="confirmSaveCharacterBtn" class="w-full mt-4 text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                                Simpan
                            </button>
                        </div>
                    </div>

                    <div id="loadCharacterModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 hidden">
                        <div class="bg-gray-800 text-gray-300 rounded-lg shadow-2xl p-6 w-full max-w-lg max-h-[80vh] overflow-y-auto">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Muat & Hapus Karakter</h2>
                                <button id="closeLoadModalBtn" class="text-gray-400 hover:text-white text-3xl">&times;</button>
                            </div>
                            <ul id="characterList" class="space-y-2">
                                <li class="text-gray-500 text-center py-4" id="noCharactersMessage">Belum ada karakter yang disimpan.</li>
                            </ul>
                            <button id="clearAllCharactersBtn" class="w-full mt-4 text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300 hidden">
                                Hapus Semua Karakter
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="aksi" class="block mb-2 text-sm font-medium text-gray-300">Aksi (Apa yang terjadi)</label>
                    <input type="text" id="aksi" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: membaca buku di bawah pohon">
                </div>

                <div>
                    <label for="ekspresi" class="block mb-2 text-sm font-medium text-gray-300">Ekspresi (Emosi)</label>
                    <input type="text" id="ekspresi" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: puas dan tersenyum">
                </div>

                <div>
                    <label for="tempat" class="block mb-2 text-sm font-medium text-gray-300">Tempat (Latar Belakang)</label>
                    <!-- <input type="text" id="tempat" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: di hutan lebat"> -->
                     <input type="text" id="tempat" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: di hutan lebat">
                    <div class="flex space-x-2 mt-2">
                        <button id="savePlaceBtn" class="flex-1 text-white bg-purple-600 hover:bg-purple-700 focus:ring-4 focus:outline-none focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Simpan Tempat
                        </button>
                        <button id="loadPlaceBtn" class="flex-1 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Muat Tempat
                        </button>
                    </div>
                    <div id="savePlaceModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 hidden">
                        <div class="bg-gray-800 text-gray-300 rounded-lg shadow-2xl p-6 w-full max-w-sm max-h-[80vh] overflow-y-auto">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Simpan Tempat</h2>
                                <button id="closeSaveModalPlaceBtn" class="text-gray-400 hover:text-white text-3xl">&times;</button>
                            </div>
                            <label for="placeNameInput" class="block mb-2 text-sm font-medium text-gray-300">Nama Tempat:</label>
                            <input type="text" id="placeNameInput" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: Rubah Cerdas">
                            <button id="confirmSavePlaceBtn" class="w-full mt-4 text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                                Simpan
                            </button>
                        </div>
                    </div>

                    <div id="loadPlaceModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 hidden">
                        <div class="bg-gray-800 text-gray-300 rounded-lg shadow-2xl p-6 w-full max-w-lg max-h-[80vh] overflow-y-auto">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Muat & Hapus Tempat</h2>
                                <button id="closeLoadModalPlaceBtn" class="text-gray-400 hover:text-white text-3xl">&times;</button>
                            </div>
                            <ul id="placeList" class="space-y-2">
                                <li class="text-gray-500 text-center py-4" id="noPlacesMessage">Belum ada tempat yang disimpan.</li>
                            </ul>
                            <button id="clearAllPlacesBtn" class="w-full mt-4 text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300 hidden">
                                Hapus Semua Tempat
                            </button>
                        </div>
                    </div>
                </div>

                 <div>
                    <label for="waktu" class="block mb-2 text-sm font-medium text-gray-300">Waktu</label>
                    <select id="waktu" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5">
                        <option selected value="">Pilih waktu...</option>
                        <option value="pagi hari">Pagi Hari</option>
                        <option value="siang hari">Siang Hari</option>
                        <option value="sore hari">Sore Hari</option>
                        <option value="senja (golden hour)">Senja (Golden Hour)</option>
                        <option value="blue hour">Blue Hour</option>
                        <option value="malam hari">Malam Hari</option>
                        <option value="tengah malam">Tengah Malam</option>
                        <option value="dini hari">Dini Hari</option>
                    </select>
                </div>

                <div>
                    <label for="sudutKamera" class="block mb-2 text-sm font-medium text-gray-300">Sudut Kamera</label>
                    <select id="sudutKamera" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5">
                        <option selected value="">Pilih sudut kamera...</option>
                        <option value="close-up shot">Close-Up Shot</option>
                        <option value="medium shot">Medium Shot</option>
                        <option value="long shot">Long Shot</option>
                        <option value="eye-level shot">Eye-Level Shot</option>
                        <option value="high-angle shot">High-Angle Shot</option>
                        <option value="low-angle shot">Low-Angle Shot</option>
                        <option value="bird's-eye view">Bird's-Eye View</option>
                        <option value="worm's-eye view">Worm's-Eye View</option>
                        <option value="dutch angle">Dutch Angle</option>
                        <option value="over-the-shoulder shot">Over-the-Shoulder Shot</option>
                    </select>
                </div>

                <div>
                    <label for="kamera" class="block mb-2 text-sm font-medium text-gray-300">Gerakan Kamera</label>
                    <select id="kamera" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5">
                        <option selected value="">Pilih Gerakan Kamera</option>
                        <option value="Pan (Geser Horizontal)">Pan (Geser Horizontal)</option>
                        <option value="Tilt (Miring Vertikal)">Tilt (Miring Vertikal)</option>
                        <option value="Dolly (Maju/Mundur di atas rel)">Dolly (Maju/Mundur di atas rel)</option>
                        <option value="Zoom In/Out (Perbesar/Perkecil)">Zoom In/Out (Perbesar/Perkecil)</option>
                        <option value="Tracking Shot (Mengikuti Subjek)">Tracking Shot (Mengikuti Subjek)</option>
                        <option value="Crane Shot (Angkat Derek)">Crane Shot (Angkat Derek)</option>
                        <option value="Handheld (Genggam)">Handheld (Genggam)</option>
                        <option value="Static (Diam/Statis)">Static (Diam/Statis)</option>
                        <option value="Orbit (Mengelilingi Subjek)">Orbit (Mengelilingi Subjek)</option>
                        <option value="Arc (Gerakan Busur)">Arc (Gerakan Busur)</option>
                        <option value="Spiral (Gerakan Spiral)">Spiral (Gerakan Spiral)</option>
                        <option value="Push In (Dorong Masuk)">Push In (Dorong Masuk)</option>
                        <option value="Pull Out (Tarik Keluar)">Pull Out (Tarik Keluar)</option>
                        <option value="Roll (Berguling)">Roll (Berguling)</option>
                        <option value="3D Rotation (Rotasi 3D)">3D Rotation (Rotasi 3D)</option>
                        <option value="POV (Sudut Pandang Subjek)">POV (Sudut Pandang Subjek)</option>
                        <option value="Fly Through (Terbang Melewati)">Fly Through (Terbang Melewati)</option>
                        <option value="Time-lapse (Selang Waktu)">Time-lapse (Selang Waktu)</option>
                        <option value="Slow Motion (Gerak Lambat)">Slow Motion (Gerak Lambat)</option>
                        <option value="Fast Motion (Gerak Cepat)">Fast Motion (Gerak Cepat)</option>
                        <option value="Reverse Zoom (Zoom Terbalik)">Reverse Zoom (Zoom Terbalik)</option>
                        <option value="Gimbal (Stabilisasi Gimbal)">Gimbal (Stabilisasi Gimbal)</option>
                    </select>
                </div>

                <div>
                    <label for="pencahayaan" class="block mb-2 text-sm font-medium text-gray-300">Pencahayaan</label>
                    <select id="pencahayaan" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5">
                        <option selected value="">Pilih pencahayaan...</option>
                        <option value="cahaya alami">Cahaya Alami</option>
                        <option value="pencahayaan tiga titik">Pencahayaan Tiga Titik</option>
                        <option value="high-key lighting">High-Key Lighting</option>
                        <option value="low-key lighting">Low-Key Lighting</option>
                        <option value="backlight (cahaya dari belakang)">Backlight</option>
                        <option value="silhouette">Silhouette</option>
                        <option value="rembrandt lighting">Rembrandt Lighting</option>
                        <option value="pencahayaan sinematik">Pencahayaan Sinematik</option>
                        <option value="cahaya lembut (soft light)">Cahaya Lembut (Soft Light)</option>
                        <option value="cahaya keras (hard light)">Cahaya Keras (Hard Light)</option>
                    </select>
                </div>

                <div>
                    <label for="style" class="block mb-2 text-sm font-medium text-gray-300">Style Video</label>
                    <select id="style" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5">
                        <option selected value="">Pilih style video...</option>
                        <option value="Sinematik 8K UHD">Sinematik 8K UHD</option>
                        <option value="Gaya film dokumenter">Gaya Film Dokumenter</option>
                        <option value="Footage drone">Footage Drone</option>
                        <option value="Gaya Ghibli">Gaya Ghibli</option>
                        <option value="Gaya Pixar">Gaya Pixar</option>
                        <option value="Animasi 2D">Animasi 2D</option>
                        <option value="Animasi 3D">Animasi 3D</option>
                        <option value="Stop Motion">Stop Motion</option>
                        <option value="Video hitam putih">Video Hitam Putih</option>
                        <option value="Gaya film vintage">Gaya Film Vintage</option>
                        <option value="Hyperrealistic">Hyperrealistic</option>
                        <option value="Timelapse">Timelapse</option>
                    </select>
                </div>

                <div>
                    <label for="suasana" class="block mb-2 text-sm font-medium text-gray-300">Suasana Video</label>
                     <select id="suasana" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5">
                         <option selected value="">Pilih suasana...</option>
                         <option value="tenang dan damai">Tenang & Damai</option>
                         <option value="bahagia dan ceria">Bahagia & Ceria</option>
                         <option value="sedih dan melankolis">Sedih & Melankolis</option>
                         <option value="tegang dan menegangkan">Tegang & Menegangkan</option>
                         <option value="misterius">Misterius</option>
                         <option value="romantis">Romantis</option>
                         <option value="epik dan megah">Epik & Megah</option>
                         <option value="magis dan fantasi">Magis & Fantasi</option>
                         <option value="nostalgia">Nostalgia</option>
                         <option value="menyeramkan">Menyeramkan</option>
                         <option value="energik dan bersemangat">Energik & Bersemangat</option>
                     </select>
                </div>

                <div>
                    <label for="backsound" class="block mb-2 text-sm font-medium text-gray-300">Backsound (Suara/Musik)</label>
                    <input type="text" id="backsound" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: suara alam, musik lo-fi">
                </div>

                <div>
                    <label for="kalimat" class="block mb-2 text-sm font-medium text-gray-300">Kalimat yang Diucapkan</label>
                    <input type="text" id="kalimat" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: 'Ini hari yang sempurna.'">
                </div>

                <div>
                    <label for="detail" class="block mb-2 text-sm font-medium text-gray-300">Detail Tambahan (spesifik atau preferensi)</label>
                    <input type="text" id="detail" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Contoh: hyperrealistic, fokus detail">
                </div>

                <!-- NEW: Negative Prompt Section -->
                <div>
                    <label for="negative" class="block mb-2 text-sm font-medium text-gray-300">Negative Prompt (Yang dihindari)</label>
                    <input type="text" id="negative" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" value="teks di layar, subtitle, tulisan di video, font, logo, distorsi, artefak, anomali, wajah ganda, anggota badan cacat, tangan tidak normal, orang tambahan, objek mengganggu, kualitas rendah, buram, glitch, suara robotik, suara pecah." placeholder="Contoh: kartun, blur, kualitas rendah, teks, watermark, deformed hands, transition">
                </div>
                 <button id="generateBtn" class="w-full mt-4 text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:outline-none focus:ring-indigo-800 font-medium rounded-lg text-sm px-5 py-3 text-center transition-colors duration-300">
                    Generate Prompt
                </button>
            </div>

            <div class="space-y-6">
                <div class="bg-gray-800/50 p-6 rounded-xl border border-gray-700 flex flex-col">
                    <h2 class="text-xl font-semibold text-white mb-4">Prompt Indonesia</h2>
                    <textarea id="promptIndonesia" readonly rows="8" class="w-full text-sm rounded-lg p-3 flex-grow resize-none focus:outline-none prompt-textarea" placeholder="Hasil prompt dalam Bahasa Indonesia..."></textarea>
                    <div class="flex mt-3 space-x-2">
                        <button id="fixPromptIdBtn" class="flex-1 text-white bg-purple-600 hover:bg-purple-700 focus:ring-4 focus:outline-none focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Perbaiki
                        </button>
                        <button id="openGeminiIdBtn" class="flex-1 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Buka Gemini
                        </button>
                        <button id="copyBtnId" class="flex-1 text-white bg-teal-600 hover:bg-teal-700 focus:ring-4 focus:outline-none focus:ring-teal-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Salin
                        </button>
                    </div>
                </div>

                <div class="bg-gray-800/50 p-6 rounded-xl border border-gray-700 flex flex-col">
                    <h2 class="text-xl font-semibold text-white mb-4">Prompt English</h2>
                    <textarea id="promptEnglish" readonly rows="8" class="w-full text-sm rounded-lg p-3 flex-grow resize-none focus:outline-none prompt-textarea" placeholder="Hasil terjemahan dalam Bahasa Inggris..."></textarea>
                    <div class="flex mt-3 space-x-2">
                         <button id="fixPromptEnBtn" class="flex-1 text-white bg-purple-600 hover:bg-purple-700 focus:ring-4 focus:outline-none focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Perbaiki
                        </button>
                         <button id="openGeminiEnBtn" class="flex-1 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Buka Gemini
                        </button>
                        <button id="copyBtnEn" class="flex-1 text-white bg-teal-600 hover:bg-teal-700 focus:ring-4 focus:outline-none focus:ring-teal-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-300">
                            Salin
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div id="guideModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 hidden">
        <div class="bg-gray-800 text-gray-300 rounded-lg shadow-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-white">Panduan Membuat Prompt</h2>
                <button id="closeGuideBtn" class="text-gray-400 hover:text-white text-3xl">&times;</button>
            </div>
            <div class="prose prose-invert max-w-none">
                <p class="bg-yellow-900/50 text-yellow-200 p-3 rounded-md"><strong>JIKA MALAS BACA, LANGSUNG PENCET TOMBOL "PERBAIKI" DI KOLOM HASIL.</strong> INTINYA PROMPT ANDA DITOLAK VEO, DAN SAYA SEDIAKAN FITUR BUAT MEMPERBAIKI PROMPT KAMU AGAR BISA DITERIMA VEO.</p>
                <h3 class="text-white">Analisis Penyebab Prompt Ditolak</h3>
                <p>Berikut adalah beberapa kemungkinan utama mengapa prompt Anda ditolak oleh Veo:</p>
                <h4 class="text-white">Pelanggaran Kebijakan (Paling Umum):</h4>
                <ul>
                    <li><strong>Menyebut Nama Orang Terkenal atau Karakter Berhak Cipta:</strong> Veo sangat ketat soal ini. Anda tidak bisa meminta video "Elon Musk mengendarai sepeda di Mars" atau "Batman sedang makan sate." Hal ini untuk menghindari misinformasi dan pelanggaran hak cipta.</li>
                    <li><strong>Kekerasan atau Konten Berbahaya:</strong> Deskripsi aksi yang terlalu detail soal perkelahian, senjata, atau adegan berbahaya akan langsung ditolak.</li>
                    <li><strong>Konten Dewasa:</strong> Subjek atau aksi yang mengarah ke konten seksual atau ketelanjangan tidak akan diproses.</li>
                </ul>
                <h4 class="text-white">Prompt Terlalu Rumit atau Bertentangan:</h4>
                <ul>
                    <li>Meskipun semua field di generator valid, mengisi semuanya dengan instruksi yang sangat detail bisa membuat AI bingung.</li>
                    <li><strong>Contoh Kontradiksi:</strong> "Seorang anak kecil tertawa terbahak-bahak, suasana sedih dan melankolis, pencahayaan low-key." Aksi, suasana, dan pencahayaan di sini saling bertentangan dan sulit untuk diwujudkan dalam satu adegan yang koheren.</li>
                </ul>
                <h4 class="text-white">Permintaan yang Masih di Luar Kemampuan AI:</h4>
                <ul>
                    <li><strong>Audio Spesifik:</strong> Meminta "backsound musik rock dari Queen" kemungkinan besar akan ditolak karena hak cipta dan kemampuan AI yang masih terbatas dalam menghasilkan musik spesifik.</li>
                    <li><strong>Teks atau Dialog yang Kompleks:</strong> Meminta subjek mengucapkan kalimat panjang dengan sinkronisasi bibir yang sempurna masih sangat sulit bagi AI.</li>
                </ul>
                <h3 class="text-white">Tips Agar Prompt Anda Diterima</h3>
                <p>Untuk meningkatkan kemungkinan prompt Anda diterima, coba beberapa strategi berikut:</p>
                <ul>
                    <li><strong>Prioritaskan & Sederhanakan:</strong> Jangan mengisi semua kolom. Pilih 3-4 elemen paling penting yang ingin Anda lihat. Fokus pada deskripsi visual.</li>
                    <li><strong>Gunakan Bahasa yang Lebih Natural:</strong> Coba ubah hasil dari generator menjadi kalimat yang lebih alami daripada hanya daftar kata kunci yang dipisahkan koma.</li>
                    <li><strong>Mulai dari yang Sederhana:</strong> Coba buat prompt dengan 2-3 elemen dulu (misalnya: Subjek, Aksi, dan Tempat). Jika berhasil, baru tambahkan elemen lain seperti gaya visual atau gerakan kamera.</li>
                </ul>
                <p>Semoga analisis dan tips ini membantu Anda membuat prompt yang lebih efektif untuk Veo 3!</p>
            </div>
        </div>
    </div>

    <footer class="text-center p-4 mt-8">
        <!-- <a href="https://t.me/+GbpYVm-4ymQ3ZjFl" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-300 mx-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="inline-block mr-1.5 align-text-bottom" viewBox="0 0 24 24">
              <path d="m21.435 2.585-4.52 18.065c-.243 1.055-1.748.88-1.992-.17L13.195 13.2l-7.23-1.728c-1.11-.26-1.07-1.785.04-2.025L21.435 2.585Z" />
              <path d="m21.435 2.585-11.02 9.638-3.187-7.245L21.435 2.585Z"/>
            </svg>
            Join Us On Telegram
        </a> -->
    </footer>

    <!-- Tautan ke file JavaScript eksternal, defer memastikan script dijalankan setelah HTML selesai di-load -->
    <script src="script.js" defer></script>

</body>
</html>
